/*
project: remittance_reports
query_name: find_latest_transaction_dates_batch
base_name: {{ base_name | sqlsafe }}
report_name: {{ report_name | sqlsafe }}
*/
SELECT
    service_offering_id
    , created_at AS transaction_date
FROM (
    SELECT
        service_offering_id
        , created_at
        , row_number() OVER (PARTITION BY service_offering_id ORDER BY created_at DESC) AS rn
    FROM {{ database_name | sqlsafe }}.remittance.braintree_transactions_v3
    WHERE service_offering_id IN {{ service_offering_ids | inclause }}
) AS ranked
WHERE rn = 1
ORDER BY service_offering_id;

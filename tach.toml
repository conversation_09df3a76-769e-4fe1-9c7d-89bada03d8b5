exclude = [
  "**/*__pycache__",
  "**/*egg-info",
  "docs",
  "tests"
]
source_roots = [
  "."
]

[external]
exclude = [
  "awscli",
  "botocore",  # need to account for this in a boto3 renaming rule, maybe
  "certifi",
  "crontab",
  "dagster_docker",
  "dagster_postgres",
  "dotenv",
  "python_dotenv",
  "freezegun",
  "fsspec",
  "grpcio",
  "honeycomb_opentelemetry",
  "jinja2",
  "kafka_python",
  "moto",
  "openpyxl",
  "pg8000",
  "pyathena",
  "pyside6",
  "pytest",
  "pytz",
  "requests",
  "s3fs",
  "slack_sdk",
  "urllib3",
  "xlrd"
]

[[interfaces]]
expose = []
from = [
  "notebooks"
]

[[interfaces]]
expose = []
from = [
  "scripts"
]

[[interfaces]]
expose = []
from = [
  "fine_reports.datastore.s3"
]

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "fine_reports"
]
path = "config"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "fine_reports",
  "payit_remittance_report",
  "fine_reports.ports.logging"
]
path = "disbursement_report"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "disbursement_report",
  "fine_reports",
  "gr_water_ebilling_reports",
  "payit_311_remittance",
  "payit_remittance_consolidator",
  "payit_remittance_report",
  "fine_reports.utilities.kubernetes",
  "fine_reports.ports.logging"
]
path = "entrypoint"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "payit_remittance_report",
  "util",
  "fine_reports.dagster.utils.runtime",
  "fine_reports.utilities.kubernetes",
  "fine_reports.ports.logging"
]
path = "fine_reports"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "entrypoint",
  "fine_reports.dagster.utils.runtime",
  "fine_reports.utilities.kubernetes",
  "fine_reports",
  "fine_reports.ports.logging"
]
path = "fine_reports.dagster"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "fine_reports",
  "payit_remittance_report",
  "report_commons",
  "fine_reports.ports.logging"
]
path = "gr_water_ebilling_reports"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = []
path = "notebooks"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "fine_reports",
  "payit_remittance_report",
  "report_commons",
  "util",
  "fine_reports.ports.logging"
]
path = "payit_311_remittance"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "payit_remittance_report",
  "fine_reports.ports.logging"
]
path = "payit_remittance_consolidator"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "fine_reports",
  "remittance",
  "report_commons",
  "util",
  "fine_reports.ports.logging"
]
path = "payit_remittance_report"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "payit_remittance_report",
  "util",
  "fine_reports.ports.logging"
]
path = "remittance"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "fine_reports",
  "payit_remittance_report",
  "fine_reports.ports.logging"
]
path = "report_commons"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "payit_remittance_report",
  "fine_reports",
  "fine_reports.dagster.migration",
  "fine_reports.ports.logging"
]
path = "scripts"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "config",
  "fine_reports"
]
path = "util"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "fine_reports.utilities.kubernetes"
]
path = "fine_reports.dagster.utils.runtime"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = []
path = "fine_reports.utilities.kubernetes"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = []
path = "fine_reports.dagster.migration"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = []
path = "fine_reports.datastore.s3"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = [
  "fine_reports.ports.logging.logger_loguru"
]
path = "fine_reports.ports.logging"

[[modules]]
cannot_depend_on_external = [
  "PySide6"
]
depends_on = []
depends_on_external = [
  "loguru"
]
path = "fine_reports.ports.logging.logger_loguru"

[[modules]]
cannot_depend_on_external = [
  "loguru",
  "PySide6"
]
depends_on = ["payit_remittance_report", "fine_reports.ports.logging", "fine_reports"]
path = "scripts.contrib"
